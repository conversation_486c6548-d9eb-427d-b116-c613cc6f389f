// Global variables to store reCAPTCHA widget IDs
let recaptchaDarkId = null;
let recaptchaLightId = null;
let recaptchaFullyLoaded = false;

// Function called when reCAPTCHA API loads - must be declared at top level
function onRecaptchaLoad() {
    console.log('reCAPTCHA API loaded, initializing...');
    initializeRecaptcha();
}

// Function to get current theme
function getCurrentTheme() {
    const htmlElement = document.documentElement;
    return htmlElement.classList.contains('dark') ? 'dark' : 'light';
}

// Function to initialize both reCAPTCHA widgets
function initializeRecaptcha() {
    console.log('Attempting to initialize reCAPTCHA...');
    const recaptchaContainer = document.getElementById('recaptcha-widget');
    if (recaptchaContainer && typeof grecaptcha !== 'undefined' && grecaptcha.render) {
        try {
            // Create containers for both themes
            recaptchaContainer.innerHTML = `
                <div id="recaptcha-dark" style="display: block;"></div>
                <div id="recaptcha-light" style="display: none;"></div>
            `;

            // Render both widgets
            recaptchaDarkId = grecaptcha.render('recaptcha-dark', {
                'sitekey': '6LdLmHMrAAAAAOjVgSLRiO4bmQx2T07gWNT01nT0',
                'theme': 'dark'
            });

            recaptchaLightId = grecaptcha.render('recaptcha-light', {
                'sitekey': '6LdLmHMrAAAAAOjVgSLRiO4bmQx2T07gWNT01nT0',
                'theme': 'light'
            });

            console.log('reCAPTCHA widgets rendered successfully');

            // Show the correct widget based on current theme
            switchRecaptchaTheme();

        // Mark reCAPTCHA as fully loaded
        recaptchaFullyLoaded = true;
        console.log('✅ reCAPTCHA fully loaded and ready');
        } catch (error) {
            console.error('Error initializing reCAPTCHA:', error);
        }
    } else {
        console.log('reCAPTCHA not ready yet');
    }
}

// Function to switch between reCAPTCHA themes
function switchRecaptchaTheme() {
    const darkWidget = document.getElementById('recaptcha-dark');
    const lightWidget = document.getElementById('recaptcha-light');
    const currentTheme = getCurrentTheme();

    if (darkWidget && lightWidget) {
        if (currentTheme === 'dark') {
            darkWidget.style.display = 'block';
            lightWidget.style.display = 'none';
        } else {
            darkWidget.style.display = 'none';
            lightWidget.style.display = 'block';
        }
    }
}

// Function to show form messages with nice styling
function showFormMessage(message, type = 'info') {
    // Remove any existing message
    const existingMessage = document.querySelector('.form-message');
    if (existingMessage) {
        existingMessage.remove();
    }

    // Create message element
    const messageDiv = document.createElement('div');
    messageDiv.className = `form-message form-message-${type}`;
    messageDiv.innerHTML = `
        <div class="form-message-content">
            <span class="form-message-icon">
                ${type === 'success' ? '✓' : type === 'error' ? '✗' : type === 'warning' ? '⚠' : 'ℹ'}
            </span>
            <span class="form-message-text">${message}</span>
            <button class="form-message-close" onclick="this.parentElement.parentElement.remove()">×</button>
        </div>
    `;

    // Insert message after the contact form
    const contactForm = document.getElementById('contact-form');
    if (contactForm) {
        contactForm.parentNode.insertBefore(messageDiv, contactForm.nextSibling);

        // Auto-remove success messages after 5 seconds
        if (type === 'success') {
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.remove();
                }
            }, 5000);
        }

        // Scroll message into view
        messageDiv.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }
}

// reCAPTCHA callback function is now declared at the top of the file

// Create a mock reCAPTCHA for local development
function createMockRecaptcha() {
    console.log('Creating mock reCAPTCHA for mobile/local development...');
    const recaptchaContainer = document.getElementById('recaptcha-widget');
    if (recaptchaContainer) {
        recaptchaContainer.innerHTML = `
            <div style="
                width: 304px;
                height: 78px;
                border: 1px solid #d3d3d3;
                background: #f9f9f9;
                display: flex;
                align-items: center;
                justify-content: center;
                font-family: Arial, sans-serif;
                color: #666;
                border-radius: 3px;
                margin: 0 auto;
            ">
                <div style="display: flex; align-items: center;">
                    <input type="checkbox" id="mock-recaptcha" style="margin-right: 10px; transform: scale(1.2);">
                    <label for="mock-recaptcha">I'm not a robot (Local Dev Mode)</label>
                </div>
            </div>
        `;

        // Set mock IDs to indicate it's working
        recaptchaDarkId = 'mock-dark';
        recaptchaLightId = 'mock-light';
        recaptchaFullyLoaded = true;
        console.log('✅ Mock reCAPTCHA ready');
    }
}

// Fallback initialization in case onRecaptchaLoad doesn't fire
function tryInitializeRecaptcha() {
    console.log('Trying fallback reCAPTCHA initialization...');
    if (typeof grecaptcha !== 'undefined' && grecaptcha.render && recaptchaDarkId === null) {
        console.log('Fallback reCAPTCHA initialization');
        initializeRecaptcha();
    } else if (recaptchaDarkId === null) {
        console.log('reCAPTCHA API not available, using mock for local development');
        createMockRecaptcha();
    }
}

// Enhanced mobile detection and fallback
function isMobileDevice() {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
           window.innerWidth <= 768;
}

// Check for reCAPTCHA errors and switch to mock if needed
function checkRecaptchaHealth() {
    // If we're on mobile and getting 401 errors, switch to mock
    if (isMobileDevice() && recaptchaDarkId !== null && recaptchaDarkId !== 'mock-dark') {
        // Check if reCAPTCHA is having issues
        setTimeout(() => {
            const recaptchaFrames = document.querySelectorAll('iframe[src*="recaptcha"]');
            let hasErrors = false;

            // Check for common error indicators
            recaptchaFrames.forEach(frame => {
                if (frame.src.includes('error') || frame.style.display === 'none') {
                    hasErrors = true;
                }
            });

            if (hasErrors || recaptchaFrames.length === 0) {
                console.log('🔄 reCAPTCHA issues detected on mobile, switching to mock...');
                createMockRecaptcha();
            }
        }, 3000);
    }
}

document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded');

    // Try to initialize reCAPTCHA if it hasn't been initialized yet (fallback)
    setTimeout(tryInitializeRecaptcha, 3000);

    // Initialize particles.js for hero section
    console.log('Particles.js available:', typeof particlesJS !== 'undefined');
    if (typeof particlesJS !== 'undefined') {
        console.log('Initializing particles...');
        particlesJS('particles-js', {
            "particles": {
                "number": {
                    "value": 80,
                    "density": {
                        "enable": true,
                        "value_area": 1000
                    }
                },
                "color": {
                    "value": "#007db6"
                },
                "shape": {
                    "type": "circle",
                    "stroke": {
                        "width": 0,
                        "color": "#000000"
                    }
                },
                "opacity": {
                    "value": 10,
                    "random": false,
                    "anim": {
                        "enable": false,
                        "speed": 1,
                        "opacity_min": 0.1,
                        "sync": false
                    }
                },
                "size": {
                    "value": 3,
                    "random": true,
                    "anim": {
                        "enable": false,
                        "speed": 30,
                        "size_min": 0.5,
                        "sync": false
                    }
                },
                "line_linked": {
                    "enable": true,
                    "distance": 150,
                    "color": "#007db6",
                    "opacity": 0.6,
                    "width": 3
                },
                "move": {
                    "enable": true,
                    "speed": 6,
                    "direction": "none",
                    "random": false,
                    "straight": false,
                    "out_mode": "out",
                    "bounce": false,
                    "attract": {
                        "enable": false,
                        "rotateX": 600,
                        "rotateY": 1200
                    }
                }
            },
            "interactivity": {
                "detect_on": "canvas",
                "events": {
                    "onhover": {
                        "enable": true,
                        "mode": "repulse"
                    },
                    "onclick": {
                        "enable": true,
                        "mode": "push"
                    },
                    "resize": true
                },
                "modes": {
                    "grab": {
                        "distance": 200,
                        "line_linked": {
                            "opacity": 1
                        }
                    },
                    "bubble": {
                        "distance": 200,
                        "size": 40,
                        "duration": 2,
                        "opacity": 8,
                        "speed": 2
                    },
                    "repulse": {
                        "distance": 200,
                        "duration": 0.4
                    },
                    "push": {
                        "particles_nb": 4
                    },
                    "remove": {
                        "particles_nb": 2
                    }
                }
            },
            "retina_detect": true
        }, function() {
            console.log('Particles.js loaded successfully!');
        });
    } else {
        console.log('Particles.js not found!');
    }

    // Theme toggle functionality
    const themeToggle = document.getElementById('theme-toggle');
    const htmlElement = document.documentElement;
    
    // Check for saved theme preference or use default dark theme
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme) {
        htmlElement.className = savedTheme;
    } else {
        // Default to dark theme if no preference is saved
        htmlElement.classList.add('dark');
        localStorage.setItem('theme', 'dark');
    }
    
    themeToggle.addEventListener('click', () => {
        if (htmlElement.classList.contains('dark')) {
            htmlElement.classList.remove('dark');
            localStorage.setItem('theme', '');
        } else {
            htmlElement.classList.add('dark');
            localStorage.setItem('theme', 'dark');
        }

        // Switch reCAPTCHA theme
        switchRecaptchaTheme();
    });
    
    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);
            
            window.scrollTo({
                top: targetElement.offsetTop - 80,
                behavior: 'smooth'
            });
        });
    });
    
    // Form submission handling
    const contactForm = document.getElementById('contact-form');
    if (contactForm) {
        contactForm.addEventListener('submit', (e) => {
            e.preventDefault();

            // Check if reCAPTCHA is completed (check the currently visible widget)
            const currentTheme = getCurrentTheme();
            const currentWidgetId = currentTheme === 'dark' ? recaptchaDarkId : recaptchaLightId;

            console.log('Form submission - Current theme:', currentTheme);
            console.log('Form submission - Widget ID:', currentWidgetId);
            console.log('Form submission - reCAPTCHA available:', typeof grecaptcha !== 'undefined');

            let recaptchaResponse = '';

            // Handle mock reCAPTCHA for local development
            if (currentWidgetId === 'mock-dark' || currentWidgetId === 'mock-light') {
                const mockCheckbox = document.getElementById('mock-recaptcha');
                if (mockCheckbox && mockCheckbox.checked) {
                    recaptchaResponse = 'mock-response-for-local-dev';
                    console.log('Mock reCAPTCHA verified');
                }
            } else if (currentWidgetId !== null && typeof grecaptcha !== 'undefined' && grecaptcha.getResponse) {
                try {
                    recaptchaResponse = grecaptcha.getResponse(currentWidgetId);
                    console.log('reCAPTCHA response:', recaptchaResponse ? 'Present' : 'Empty');
                    if (recaptchaResponse) {
                        console.log('reCAPTCHA response length:', recaptchaResponse.length);
                        console.log('reCAPTCHA response preview:', recaptchaResponse.substring(0, 50) + '...');
                    }
                } catch (error) {
                    console.error('Error getting reCAPTCHA response:', error);
                }
            }

            if (!recaptchaResponse) {
                showFormMessage('Please complete the reCAPTCHA verification.', 'warning');
                return;
            }

            // Send form data to server
            const formData = new FormData(contactForm);

            // Add reCAPTCHA response to form data
            formData.append('g-recaptcha-response', recaptchaResponse);

            console.log('Sending form data to server...');
            console.log('reCAPTCHA data being sent:', recaptchaResponse === 'mock-response-for-local-dev' ? 'MOCK DATA' : 'REAL reCAPTCHA TOKEN');

            // Log all form data being sent (for debugging)
            console.log('Form data contents:');
            for (let [key, value] of formData.entries()) {
                if (key === 'g-recaptcha-response') {
                    console.log(`  ${key}:`, value === 'mock-response-for-local-dev' ? 'MOCK' : `${value.substring(0, 20)}...`);
                } else {
                    console.log(`  ${key}:`, value);
                }
            }

            // Show loading state with spinner
            const submitButton = contactForm.querySelector('button[type="submit"]');
            const originalButtonText = submitButton.textContent;
            submitButton.innerHTML = `
                <svg class="spinner" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" stroke-linecap="round" stroke-dasharray="31.416" stroke-dashoffset="31.416">
                        <animate attributeName="stroke-dasharray" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite"/>
                        <animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite"/>
                    </circle>
                </svg>
                Sending...
            `;
            submitButton.disabled = true;

            // Send data to PHP script
            fetch('/php/send_email.php', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                // Check if response is actually JSON
                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    return response.text().then(text => {
                        console.error('Non-JSON response received:', text);
                        throw new Error('Server returned non-JSON response');
                    });
                }

                return response.json();
            })
            .then(data => {
                console.log('Server response:', data);

                if (data.success) {
                    // Reset form on success
                    contactForm.reset();

                    // Reset reCAPTCHA widgets
                    if (recaptchaDarkId === 'mock-dark' || recaptchaLightId === 'mock-light') {
                        // Reset mock reCAPTCHA
                        const mockCheckbox = document.getElementById('mock-recaptcha');
                        if (mockCheckbox) {
                            mockCheckbox.checked = false;
                        }
                    } else {
                        // Reset real reCAPTCHA widgets
                        if (recaptchaDarkId !== null && typeof grecaptcha !== 'undefined') {
                            grecaptcha.reset(recaptchaDarkId);
                        }
                        if (recaptchaLightId !== null && typeof grecaptcha !== 'undefined') {
                            grecaptcha.reset(recaptchaLightId);
                        }
                    }

                    showFormMessage('Thank you for your message! We will get back to you soon.', 'success');
                } else {
                    showFormMessage('Sorry, there was an error sending your message: ' + (data.message || 'Please try again later.'), 'error');
                }
            })
            .catch(error => {
                console.error('Error sending form:', error);
                showFormMessage('Sorry, there was an error sending your message. Please try again later.', 'error');
            })
            .finally(() => {
                // Restore button state
                submitButton.textContent = originalButtonText;
                submitButton.disabled = false;
            });
        });
    }


    // Mobile menu toggle
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    const navMenu = document.querySelector('nav ul');

    if (mobileMenuToggle) {
        mobileMenuToggle.addEventListener('click', () => {
            navMenu.classList.toggle('active');
        });
    }

    // Close mobile menu when clicking on a nav link
    document.querySelectorAll('nav ul li a').forEach(link => {
        link.addEventListener('click', () => {
            if (window.innerWidth <= 768) {
                navMenu.classList.remove('active');
            }
        });
    });

    // Video testimonials functionality
    const videoModal = document.getElementById('video-modal');
    const modalVideo = document.getElementById('modal-video');
    const modalTitle = document.getElementById('video-modal-title');
    const modalClose = document.querySelector('.video-modal-close');
    const videoTestimonials = document.querySelectorAll('.video-testimonial');
    const videoLoading = document.getElementById('video-loading');

    // Add debugging event listeners to modal video
    modalVideo.addEventListener('error', (e) => {
        // Ignore "empty src" errors that occur when closing modal
        if (modalVideo.error && modalVideo.error.code === 4 && modalVideo.error.message.includes('Empty src')) {
            return; // Silently ignore this expected error
        }
        console.error('❌ Modal video error:', e);
        console.error('Error details:', modalVideo.error);
    });

    modalVideo.addEventListener('stalled', () => {
        console.warn('⚠️ Modal video stalled');
    });

    modalVideo.addEventListener('waiting', () => {
        console.warn('⚠️ Modal video waiting for data');
    });

    modalVideo.addEventListener('playing', () => {
        console.log('▶️ Modal video started playing');
        if (videoLoading) videoLoading.style.display = 'none';
    });

    modalVideo.addEventListener('pause', () => {
        console.log('⏸️ Modal video paused');
    });

    modalVideo.addEventListener('waiting', () => {
        console.warn('⚠️ Modal video waiting for data');
        if (videoLoading) videoLoading.style.display = 'block';
    });

    modalVideo.addEventListener('canplay', () => {
        console.log('📹 Modal video can play');
        if (videoLoading) videoLoading.style.display = 'none';
    });

    // Set background images for video thumbnails
    videoTestimonials.forEach(testimonial => {
        const thumbnailSrc = testimonial.getAttribute('data-thumbnail');
        const videoThumbnail = testimonial.querySelector('.video-thumbnail');
        if (thumbnailSrc && videoThumbnail) {
            videoThumbnail.style.backgroundImage = `linear-gradient(135deg, rgba(0,125,182,0.4), rgba(0,93,133,0.6)), url('${thumbnailSrc}')`;
        }
    });

    // Video preloading disabled - videos will load on-demand when clicked

    // Open video modal
    videoTestimonials.forEach(testimonial => {
        testimonial.addEventListener('click', () => {
            const videoSrc = testimonial.getAttribute('data-video');
            const videoName = testimonial.getAttribute('data-name');

            console.log(`🎬 Opening video modal for ${videoName} - ${videoSrc}`);

            // Set up the modal
            modalTitle.textContent = `${videoName}'s Testimonial`;
            modalVideo.src = videoSrc;
            videoModal.style.display = 'block';
            if (videoLoading) videoLoading.style.display = 'block';

            // Prevent body scrolling when modal is open
            document.body.style.overflow = 'hidden';

            // Simple autoplay attempt after a short delay
            setTimeout(() => {
                modalVideo.play().then(() => {
                    console.log(`✅ ${videoName}'s video started playing`);
                }).catch(error => {
                    console.log(`ℹ️ Autoplay prevented for ${videoName}, user can click play manually:`, error.message);
                });
            }, 500);
        });
    });

    // Close video modal
    function closeVideoModal() {
        videoModal.style.display = 'none';
        modalVideo.pause();
        modalVideo.src = ''; // This will trigger an "empty src" error, but it's filtered out
        if (videoLoading) videoLoading.style.display = 'none';
        document.body.style.overflow = 'auto';
        console.log('🔒 Video modal closed');
    }

    // Close modal when clicking close button
    modalClose.addEventListener('click', closeVideoModal);

    // Close modal when clicking outside the video content
    videoModal.addEventListener('click', (e) => {
        if (e.target === videoModal) {
            closeVideoModal();
        }
    });

    // Close modal with Escape key
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && videoModal.style.display === 'block') {
            closeVideoModal();
        }
    });

    // Handle fullscreen functionality
    modalVideo.addEventListener('dblclick', () => {
        if (modalVideo.requestFullscreen) {
            modalVideo.requestFullscreen();
        } else if (modalVideo.webkitRequestFullscreen) {
            modalVideo.webkitRequestFullscreen();
        } else if (modalVideo.msRequestFullscreen) {
            modalVideo.msRequestFullscreen();
        }
    });

    // Check reCAPTCHA health on mobile devices
    if (isMobileDevice()) {
        console.log('📱 Mobile device detected, monitoring reCAPTCHA health...');
        setTimeout(checkRecaptchaHealth, 5000);
    }
});

