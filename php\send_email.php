<?php
// Set proper content type for JSON response
header('Content-Type: application/json');

// Enable error reporting for debugging (remove in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

if ($_SERVER["REQUEST_METHOD"] == "POST") {

    $ip = get_client_ip();

    // Get reCAPTCHA response
    $recaptcha_response = isset($_POST['g-recaptcha-response']) ? $_POST['g-recaptcha-response'] : '';

    // Debug logging
    error_log("=== reCAPTCHA Debug Info ===");
    error_log("reCAPTCHA response received: " . ($recaptcha_response ? "YES (length: " . strlen($recaptcha_response) . ")" : "NO"));
    if ($recaptcha_response && $recaptcha_response !== 'mock-response-for-local-dev') {
        error_log("reCAPTCHA response preview: " . substr($recaptcha_response, 0, 50) . "...");
    }
    error_log("Client IP: " . $ip);

    // Handle mock reCAPTCHA for local development
    if ($recaptcha_response === 'mock-response-for-local-dev') {
        // Skip reCAPTCHA verification for local development
        error_log("Using mock reCAPTCHA for local development");
    } else {
        // Verify reCAPTCHA using cURL
        $recaptcha_secret = '6LdLmHMrAAAAABpG024MWsuny5pwCjxA4trhq7G-';

        if (empty($recaptcha_response)) {
            echo json_encode(['success' => false, 'message' => 'reCAPTCHA response is required']);
            exit;
        }

        $recaptcha_json = verify_recaptcha($recaptcha_secret, $recaptcha_response, $ip);

        // Log reCAPTCHA verification result
        error_log("reCAPTCHA verification result: " . json_encode($recaptcha_json));

        if (!$recaptcha_json || !isset($recaptcha_json['success']) || !$recaptcha_json['success']) {
            $error_message = 'reCAPTCHA verification failed';
            if (isset($recaptcha_json['error-codes'])) {
                $error_message .= ': ' . implode(', ', $recaptcha_json['error-codes']);
                error_log("reCAPTCHA error codes: " . implode(', ', $recaptcha_json['error-codes']));
            }
            echo json_encode(['success' => false, 'message' => $error_message]);
            exit;
        }

        error_log("reCAPTCHA verification successful!");
    }

    // Sanitize inputs (using htmlspecialchars instead of deprecated FILTER_SANITIZE_STRING)
    $name = isset($_POST['name']) ? htmlspecialchars(trim($_POST['name']), ENT_QUOTES, 'UTF-8') : '';
    $email = filter_input(INPUT_POST, 'email', FILTER_SANITIZE_EMAIL);
    $phone = isset($_POST['phone']) ? htmlspecialchars(trim($_POST['phone']), ENT_QUOTES, 'UTF-8') : '';
    $message = isset($_POST['message']) ? htmlspecialchars(trim($_POST['message']), ENT_QUOTES, 'UTF-8') : '';
    $user_ip = $ip;

    // Validate required fields
    if (empty($name)) {
        echo json_encode(['success' => false, 'message' => 'Name is required']);
        exit;
    }

    if (empty($email)) {
        echo json_encode(['success' => false, 'message' => 'Email is required']);
        exit;
    }

    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        echo json_encode(['success' => false, 'message' => 'Invalid email address']);
        exit;
    }

    if (empty($message)) {
        echo json_encode(['success' => false, 'message' => 'Message is required']);
        exit;
    }

    //$to = "<EMAIL>";
    $to = "<EMAIL>";

    $subject = "Prestige Financial & Co Website Contact Form Enquiry";

    $email_message = "Name: $name\n\n";
    $email_message .= "Email: $email\n\n";
    $email_message .= "Phone: $phone\n\n";
    $email_message .= "Message: $message\n\n";
    $email_message .= "Form was submitted from IP Address: $user_ip\n";

    $headers = "From: Prestige Financial & Co Website <<EMAIL>>\r\n";
    $headers .= "Reply-To: $email\r\n";
    $headers .= "MIME-Version: 1.0\r\n";
    $headers .= "Content-type: text/plain; charset=UTF-8\r\n";
    $headers .= "X-Mailer: PHP/" . phpversion();

    if (mail($to, $subject, $email_message, $headers)) {
        echo json_encode(['success' => true]);
    } else {
        error_log("Mail failed from: $email");
        echo json_encode(['success' => false, 'message' => 'Failed to send email']);
    }

} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
}


function get_client_ip()
{
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        return $_SERVER['HTTP_CLIENT_IP'];
    }

    if (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        // Could be a comma-separated list — take the first one
        $ipList = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
        return trim($ipList[0]);
    }

    if (!empty($_SERVER['HTTP_CF_CONNECTING_IP'])) {
        return $_SERVER['HTTP_CF_CONNECTING_IP'];
    }

    return $_SERVER['REMOTE_ADDR'];
}

function curl_get_contents($url)
{
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    $data = curl_exec($ch);
    curl_close($ch);
    return $data;
}

function verify_recaptcha($secret, $response, $remoteip)
{
    $url = 'https://www.google.com/recaptcha/api/siteverify';
    $data = [
        'secret' => $secret,
        'response' => $response,
        'remoteip' => $remoteip
    ];

    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);

    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    if ($result === false || !empty($error)) {
        error_log("reCAPTCHA cURL error: " . $error);
        return false;
    }

    if ($httpCode !== 200) {
        error_log("reCAPTCHA HTTP error: " . $httpCode);
        return false;
    }

    $decoded = json_decode($result, true);
    if ($decoded === null) {
        error_log("reCAPTCHA JSON decode error");
        return false;
    }

    return $decoded;
}
?>