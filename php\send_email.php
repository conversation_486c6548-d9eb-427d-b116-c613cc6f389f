<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

if ($_SERVER["REQUEST_METHOD"] == "POST") {

    
    $ip = get_client_ip();

    // Verify reCAPTCHA using cURL
    $recaptcha_secret = '6Lffa0wrAAAAAIgHxxlClB2gSDdXP137fpGR0IXV';
    $recaptcha_response = $_POST['g-recaptcha-response'];
    $recaptcha_json = verify_recaptcha($recaptcha_secret, $recaptcha_response, $ip);

    if (!$recaptcha_json['success']) {
        echo json_encode(['success' => false, 'message' => 'reCAPTCHA verification failed']);
        exit;
    }

    // Sanitize inputs
    $name = filter_input(INPUT_POST, 'name', FILTER_SANITIZE_STRING);
    $email = filter_input(INPUT_POST, 'email', FILTER_SANITIZE_EMAIL);
    $phone = filter_input(INPUT_POST, 'phone', FILTER_SANITIZE_STRING);
    $message = filter_input(INPUT_POST, 'message', FILTER_SANITIZE_STRING);
    $user_ip = $ip;

    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        echo json_encode(['success' => false, 'message' => 'Invalid email address']);
        exit;
    }

    $to = "<EMAIL>";

    $subject = "Prestige Financial & Co Website Contact Form Enquiry";

    $email_message = "Name: $name\n\n";
    $email_message .= "Email: $email\n\n";
    $email_message .= "Phone: $phone\n\n";
    $email_message .= "Message: $message\n\n";
    $email_message .= "Form was submitted from IP Address: $user_ip\n";

    $headers = "From: Prestige Financial & Co Website <<EMAIL>>\r\n";
    $headers .= "Reply-To: $email\r\n";
    $headers .= "MIME-Version: 1.0\r\n";
    $headers .= "Content-type: text/plain; charset=UTF-8\r\n";
    $headers .= "X-Mailer: PHP/" . phpversion();

    if (mail($to, $subject, $email_message, $headers)) {
        echo json_encode(['success' => true]);
    } else {
        error_log("Mail failed from: $email");
        echo json_encode(['success' => false, 'message' => 'Failed to send email']);
    }

} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
}


function get_client_ip()
{
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        return $_SERVER['HTTP_CLIENT_IP'];
    }

    if (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        // Could be a comma-separated list — take the first one
        $ipList = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
        return trim($ipList[0]);
    }

    if (!empty($_SERVER['HTTP_CF_CONNECTING_IP'])) {
        return $_SERVER['HTTP_CF_CONNECTING_IP'];
    }

    return $_SERVER['REMOTE_ADDR'];
}

function curl_get_contents($url)
{
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    $data = curl_exec($ch);
    curl_close($ch);
    return $data;
}

function verify_recaptcha($secret, $response, $remoteip)
{
    $url = 'https://www.google.com/recaptcha/api/siteverify';
    $data = [
        'secret' => $secret,
        'response' => $response,
        'remoteip' => $remoteip
    ];
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $result = curl_exec($ch);
    curl_close($ch);
    return json_decode($result, true);
}
?>